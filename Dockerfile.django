# set up environment variables
FROM python:3.12.4

ENV POETRY_VIRTUALENVS_CREATE false
ENV POETRY_VERSION 1.8.3
ENV POETRY_HOME /opt/poetry
ENV POETRY_NO_INTERACTION 1
ENV VIRTUAL_ENV /venv
ENV PYTHONUNBUFFERED 1

ENV PATH $POETRY_HOME/bin:$PATH

# set up dependencies, create virtual environment
RUN mkdir -p /opt/csm_web
WORKDIR /opt/csm_web

# install poetry
RUN curl -sSL https://install.python-poetry.org | python3 -
# create and activate virtual environment for poetry
RUN python3 -m venv $VIRTUAL_ENV
ENV PATH $VIRTUAL_ENV/bin:$PATH

# install python dependencies
COPY poetry.lock pyproject.toml ./
RUN poetry install --no-root --with=prod

# start database
COPY ./docker-django-entrypoint.sh ./
ENTRYPOINT ["./docker-django-entrypoint.sh"]
