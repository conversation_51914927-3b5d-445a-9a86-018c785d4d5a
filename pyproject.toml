### project info ###

[tool.poetry]
name = "csm-web"
version = "0.1.0"
description = ""
authors = []
readme = "README.md"

### dependencies ###

[tool.poetry.dependencies]
python = "~3.12.4"
# AWS
boto3 = "^1.34.144"
# django
django = "^5.0.7"
django-csp = "^3.8"
django-extensions = "^3.2.3"
django-postgres-extra = "^2.0.8"
django-storages = "^1.14.4"
djangorestframework = "^3.15.2"
djangorestframework-camel-case = "^1.4.2"
drf-nested-forms = "^1.1.8"
# factories
factory-boy = "^3.3.0"
# misc
networkx = "^3.3"

# packages for testing
pytest = "^8.2.2"
pytest-django = "^4.8.0"
freezegun = "^1.5.1"
# console
ipython = "^8.26.0"

# prod dependencies that are still imported
sentry-sdk = "^2.9.0"
social-auth-app-django = "^5.4.2"

[tool.poetry.group.dev.dependencies]
# formatting/linting
black = "^24.4.2"
isort = "^5.13.2"
pre-commit = "^3.7.1"
pylint = "^3.2.5"
pylint-django = "^2.5.5"

[tool.poetry.group.prod]
# mark prod as optional; these are not needed during development
optional = true

[tool.poetry.group.prod.dependencies]
# django
django-heroku = "^0.3.1"
psycopg2-binary = "^2.9.9"
# heroku
gunicorn = "^22.0.0"

### tools ###

[tool.isort]
profile = "black"

[tool.black]
preview = true

[tool.pylint.MASTER]
load-plugins = "pylint_django"
# don't enforce module naming
module-naming-style = "any"
# ensure app imports are handled correctly
init-hook = "import sys; sys.path.insert(0, 'csm_web')"


[tool.pylint."MESSAGES CONTROL"]
# design: num. branches, num. lines, etc
# similarities: simiar chunks of code
# C0114: missing module docstring
# C0115: missing class docstring
disable = "design,similarities,C0114,C0115"

[tool.pylint.PYLINT_DJANGO]
django-settings-module = "csm_web.settings"
