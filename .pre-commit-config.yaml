repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: check-merge-conflict
      - id: detect-private-key
      - id: end-of-file-fixer
      - id: mixed-line-ending
      - id: trailing-whitespace
  # python
  - repo: https://github.com/psf/black
    rev: 24.4.2
    hooks:
      - id: black
  - repo: https://github.com/PyCQA/isort
    rev: 5.13.2
    hooks:
      - id: isort
  - repo: local
    hooks:
      - id: pylint
        name: pylint
        entry: docker compose exec -T django pylint
        language: system
        types: [python]
        args: [
            "-rn" # only display messages
          ]
        verbose: true # always display output
  # dependencies
  - repo: https://github.com/python-poetry/poetry
    rev: 1.7.0
    hooks:
      - id: poetry-check
      - id: poetry-export
        args: ["-f", "requirements.txt", "-o", "requirements.txt", "--without-hashes", "--with", "prod"]
  # js
  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v3.1.0
    hooks:
      - id: prettier
  - repo: https://github.com/pre-commit/mirrors-eslint
    rev: v8.54.0
    hooks:
      - id: eslint
        exclude: 'webpack\.config\.js'
        verbose: true # always display output
        files: \.[jt]sx?$ # *.js, *.jsx, *.ts and *.tsx
        types: [file]
        args: ["--fix"]
  # (s)css
  - repo: https://github.com/thibaudcolas/pre-commit-stylelint
    rev: v15.11.0
    hooks:
      - id: stylelint
        args: ["--fix"]
        exclude: "csm_web/frontend/src/css/fontawesome-styles.scss"
