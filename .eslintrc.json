{"parser": "@typescript-eslint/parser", "parserOptions": {"ecmaFeatures": {"jsx": true}}, "plugins": ["@typescript-eslint", "react", "react-hooks"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:react/recommended", "plugin:import/recommended", "plugin:import/typescript", "prettier"], "rules": {"react/prop-types": [2], "react-hooks/rules-of-hooks": "error", "@typescript-eslint/no-non-null-assertion": "off", "@typescript-eslint/no-unused-vars": ["warn", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_", "caughtErrorsIgnorePattern": "^_"}], "import/order": ["error", {"groups": ["builtin", "external", "internal", "parent", "sibling", "index", "object", "type", "unknown"], "pathGroups": [{"pattern": "*.+(svg|jpg|jpeg)", "group": "unknown", "patternOptions": {"matchBase": true, "dot": true, "nocomment": true}, "position": "after"}, {"pattern": "*.+(css|scss)", "group": "unknown", "patternOptions": {"matchBase": true, "dot": true, "nocomment": true}, "position": "after"}], "alphabetize": {"order": "asc"}, "warnOnUnassignedImports": true}]}, "env": {"es6": true, "browser": true}, "settings": {"react": {"version": "18.2"}, "import/resolver": {"typescript": true, "node": true}}}