# Generated by Django 3.1.5 on 2021-08-03 04:23

from django.db import migrations, models
from django.db.migrations.operations.fields import AlterField
import django.db.models.deletion

from scheduler.models import Attendance, SectionOccurrence

# Move Attendance.date data to SectionOccurrence.date


def forward_date(apps, schema_editor):
    Attendance = apps.get_model('scheduler', 'Attendance')
    SectionOccurrence = apps.get_model('scheduler', 'SectionOccurrence')
    for attendance in Attendance.objects.all():
        # create new secOcr instance
        sectionOccurrence, _ = SectionOccurrence.objects.get_or_create(
            section=attendance.student.section,
            date=attendance.date,
        )
        # assign new secOcr to attendance
        attendance.sectionOccurrence = sectionOccurrence
        attendance.save()

# Removing SecOcr object and putting date back to Attendance


def reverse_date(apps, schema_editor):
    Attendance = apps.get_model('scheduler', 'Attendance')
    for attendance in Attendance.objects.all():
        attendance.date = attendance.sectionOccurrence.date
        attendance.save()


class Migration(migrations.Migration):

    dependencies = [
        ('scheduler', '0017_auto_20200914_1108'),
    ]

    operations = [
        # Step 1: Create SO model
        migrations.CreateModel(
            name='SectionOccurrence',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
            ],
            options={
                'ordering': ('date',),
            },
        ),
        migrations.AlterModelOptions(
            name='attendance',
            options={'ordering': ('sectionOccurrence',)},
        ),
        migrations.RemoveIndex(
            model_name='attendance',
            name='scheduler_a_date_e76868_idx',
        ),
        migrations.AddField(
            model_name='sectionoccurrence',
            name='section',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='scheduler.section'),
        ),

        # Step 2: add nullable foreign key field 'sectionOccurrence' to Attendance model
        migrations.AddField(
            model_name='attendance',
            name='sectionOccurrence',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE,
                                    to='scheduler.sectionoccurrence'),
        ),

        # Step 3: set Attendance.date field to be removed nullable
        migrations.AlterField(
            model_name='attendance',
            name='date',
            field=models.DateField(null=True),
        ),

        # Step 4:
        migrations.RunPython(forward_date, reverse_code=reverse_date),

        # Step 5: set 'sectionOccurrence' in Attendance back to non-nullable
        migrations.AlterField(
            model_name='attendance',
            name='sectionOccurrence',
            field=models.ForeignKey(null=False, on_delete=django.db.models.deletion.CASCADE,
                                    to='scheduler.sectionoccurrence'),
        ),

        migrations.AlterUniqueTogether(
            name='attendance',
            unique_together={('sectionOccurrence', 'student')},
        ),

        migrations.RemoveField(
            model_name='attendance',
            name='date',
        ),
    ]
