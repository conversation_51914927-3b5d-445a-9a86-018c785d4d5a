# Generated by Django 3.1.5 on 2021-08-12 23:43

from django.db import migrations, models
import django.db.models.deletion
import scheduler.models


class Migration(migrations.Migration):

    dependencies = [
        ('scheduler', '0019_auto_20210721_0855'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='resource',
            name='solution_file',
        ),
        migrations.RemoveField(
            model_name='resource',
            name='worksheet_file',
        ),
        migrations.RemoveField(
            model_name='resource',
            name='worksheet_name',
        ),
        migrations.CreateModel(
            name='Worksheet',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('worksheet', models.FileField(blank=True, upload_to=scheduler.models.worksheet_path)),
                ('solution', models.FileField(blank=True, upload_to=scheduler.models.worksheet_path)),
                ('resource', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='scheduler.resource')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
