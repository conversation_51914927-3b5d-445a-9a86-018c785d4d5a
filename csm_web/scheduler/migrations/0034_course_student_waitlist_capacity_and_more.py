# Generated by Django 5.1.6 on 2025-09-30 01:35

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("scheduler", "0033_matcherslot_description"),
    ]

    operations = [
        migrations.AddField(
            model_name="course",
            name="max_waitlist_enroll",
            field=models.PositiveSmallIntegerField(default=3),
        ),
        migrations.AddField(
            model_name="section",
            name="max_waitlist_capacity",
            field=models.PositiveSmallIntegerField(default=3),
        ),
        migrations.CreateModel(
            name="WaitlistedStudent",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "active",
                    models.BooleanField(
                        default=True,
                        help_text="An inactive student is a dropped student.",
                    ),
                ),
                ("timestamp", models.DateTimeField(auto_now_add=True)),
                (
                    "position",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="Manual position on the waitlist. "
                        "Lower numbers have higher priority.",
                        null=True,
                    ),
                ),
                (
                    "course",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="scheduler.course",
                    ),
                ),
                (
                    "section",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="waitlist_set",
                        to="scheduler.section",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["position", "timestamp"],
            },
        ),
    ]
