# Generated by Django 3.1.5 on 2021-07-17 02:23

from django.db import migrations, models
import django.db.models.deletion
import scheduler.models


class Migration(migrations.Migration):

    dependencies = [
        ('scheduler', '0017_auto_20200914_1108'),
    ]

    operations = [
        migrations.CreateModel(
            name='Resource',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('week_num', models.PositiveSmallIntegerField()),
                ('date', models.DateField()),
                ('topics', models.CharField(blank=True, max_length=100)),
                ('worksheet_name', models.CharField(blank=True, max_length=100)),
                ('worksheet_file', models.FileField(upload_to=scheduler.models.worksheet_path)),
                ('solution_file', models.FileField(upload_to=scheduler.models.worksheet_path)),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='scheduler.course')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
