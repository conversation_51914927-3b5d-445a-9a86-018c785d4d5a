# Generated by Django 3.2.6 on 2022-01-24 05:49

from django.db import migrations, models
import django.db.models.deletion


def copy_section_course_to_student(apps, schema_editor):
    Student = apps.get_model("scheduler", "Student")
    Section = apps.get_model("scheduler", "Section")
    for student in Student.objects.all():
        student.course = student.section.mentor.course
        student.save()
    assert Student.objects.filter(course__isnull=True).count() == 0


class Migration(migrations.Migration):

    dependencies = [
        ("scheduler", "0024_auto_20211123_2327"),
    ]

    operations = [
        migrations.AddField(
            model_name="student",
            name="course",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                to="scheduler.course",
                null=True,  # temporarily allow null values for migration
            ),
            preserve_default=False,
        ),
        migrations.RunPython(
            copy_section_course_to_student, reverse_code=migrations.RunPython.noop
        ),
        migrations.Alter<PERSON>ield(
            model_name="student",
            name="course",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                to="scheduler.course",
                null=False,  # no longer allow null values
            ),
            preserve_default=False,
        ),
    ]
