# Generated by Django 3.2.6 on 2022-04-24 10:33

from django.db import migrations, models
import django.db.models.deletion
import scheduler.models


class Migration(migrations.Migration):

    dependencies = [
        ('scheduler', '0026_user_priority_enrollment'),
    ]

    operations = [
        migrations.CreateModel(
            name='Matcher',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('assignment', models.JSONField()),
                ('is_open', models.BooleanField(default=False)),
                ('course', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='scheduler.course')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AlterField(
            model_name='section',
            name='mentor',
            field=scheduler.models.OneToOneOrNoneField(
                blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='scheduler.mentor'),
        ),
        migrations.CreateModel(
            name='MatcherSlot',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('times', models.JSONField()),
                ('min_mentors', models.PositiveSmallIntegerField()),
                ('max_mentors', models.PositiveSmallIntegerField()),
                ('matcher', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='scheduler.matcher')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='MatcherPreference',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('preference', models.PositiveSmallIntegerField()),
                ('mentor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='scheduler.mentor')),
                ('slot', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='scheduler.matcherslot')),
            ],
            options={
                'unique_together': {('slot', 'mentor')},
            },
        ),
    ]
