"""
Creates section objects from a csv generated by our matcher tools.
The expected input will have columns "person_id", "0", and "1"; "person_id" is the person's email,
"0" is the first section time, and if "1" exists, it is the second section time.
"room_0" and "room_1" columns are also concatenated to the matcher output as needed.

Times are similar to "Thursday [5:00 PM]", or "Tuesday [9:30 AM]".

For sections that do not require room bookings, see createlibrarysections.py
"""

import csv
import datetime as dt
from django.core.management import BaseCommand
from django.db import transaction, IntegrityError
from scheduler.management.commands.params import Courses  # pylint: disable=E0401
from scheduler.models import Course, User, Section, Mentor, Spacetime  # pylint: disable=E0401


class Cols:
    EMAIL = "person_id"
    FIRST_SECTION_TIME = "0"
    FIRST_SECTION_ROOM = "room_0"
    SECOND_SECTION_TIME = "1"
    SECOND_SECTION_ROOM = "room_1"


def alcove_restriction(spacetime): return 4 if "Soda 283" in spacetime.location else 5


# gets capacity of a section
CAPACITY_FUNCS = {
    Courses.CS70: lambda _: 5,
    Courses.CS88: lambda _: 4,
    Courses.CS61B: alcove_restriction,
    Courses.EE16A: lambda _: 5,
    Courses.EE16B: alcove_restriction,
    Courses.CS61C: alcove_restriction,
    Courses.CS61A: alcove_restriction
}
# gets duration of a section
one_and_half_hr = dt.timedelta(hours=1, minutes=30)
one_hr = dt.timedelta(hours=1)
DURATIONS = {
    Courses.CS70: one_and_half_hr,
    Courses.CS88: one_hr,
    Courses.CS61B: one_hr,
    Courses.EE16A: one_and_half_hr,
    Courses.EE16B: one_and_half_hr,
    Courses.CS61C: one_hr,
    Courses.CS61A: one_hr
}

DAY_OF_WEEK_DICT = {
    "Monday": Spacetime.MONDAY,
    "Tuesday": Spacetime.TUESDAY,
    "Wednesday": Spacetime.WEDNESDAY,
    "Thursday": Spacetime.THURSDAY,
    "Friday": Spacetime.FRIDAY
}


def parse_time(timestring, room, course_name):
    """Returns a spacetime from a string of the form "Thursday [5:00 PM]"""
    day_of_week = DAY_OF_WEEK_DICT[timestring.split(" ")[0]]
    start_time = dt.datetime.strptime(timestring, "%A [%I:%M %p]").time()
    return Spacetime.objects.create(
        location=room.replace("-", " "),
        start_time=start_time,
        duration=DURATIONS[course_name],
        day_of_week=day_of_week
    )


class Command(BaseCommand):
    generated = []

    def add_arguments(self, parser):
        parser.add_argument("csv_path", type=str, help="the path to the csv file to be read")
        parser.add_argument("course_name", type=str, help="the slug of the course to make sections for")

    def handle(self, *args, **options):
        filename = options["csv_path"]
        course_name = options["course_name"]
        count = 0
        with open(filename) as csvfile:
            reader = csv.DictReader(csvfile)
            try:
                with transaction.atomic():
                    for row in reader:
                        count += self.create_section(course_name, row)
                    self.stdout.write("Generated these sections:")
                    for p in self.generated:
                        self.stdout.write("{}".format(p))
                    prompt = input("Confirm creation of these sections [y/n]: ")
                    if prompt != "y":
                        raise Exception()
            except IntegrityError as e:
                self.stderr.write(f"Failed after generating {count} section(s).")
                raise e
            self.stdout.write("Generated {} section(s).".format(count))

    def create_section(self, course_name, row):
        email = row[Cols.EMAIL]
        chunks = email.split("@")
        if len(chunks) != 2:
            raise Exception("Malformed email: {}".format(email))
        if chunks[1] != "berkeley.edu":
            raise Exception("Non-Berkeley email found: {}".format(email))
        course = Course.objects.get(name=course_name)
        user, _ = User.objects.get_or_create(username=chunks[0], email=email)
        spacetime_0 = parse_time(row[Cols.FIRST_SECTION_TIME], row[Cols.FIRST_SECTION_ROOM], course_name)
        profile_0 = Mentor.objects.create(user=user)
        section_0 = Section.objects.create(
            course=course,
            spacetime=spacetime_0,
            capacity=CAPACITY_FUNCS[course_name](spacetime_0),
            mentor=profile_0
        )
        self.generated.append(section_0)
        if Cols.SECOND_SECTION_TIME in row and row[Cols.SECOND_SECTION_TIME] != "":
            spacetime_1 = parse_time(row[Cols.SECOND_SECTION_TIME], row[Cols.SECOND_SECTION_ROOM], course_name)
            profile_1 = Mentor.objects.create(user=user)
            section_1 = Section.objects.create(
                course=course,
                spacetime=spacetime_1,
                capacity=CAPACITY_FUNCS[course_name](spacetime_1),
                mentor=profile_1
            )
            self.generated.append(section_1)
            return 2
        return 1
