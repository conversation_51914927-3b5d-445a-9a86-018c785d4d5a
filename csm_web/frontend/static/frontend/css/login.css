/*
Login styles.

Defined in CSS, since the login page is standalone.
*/

#login-container {
	font-family: "Montserrat", sans-serif;

	width: 100%;
	height: 100%;
	margin: auto;
	text-align: center;
}

#login-card {
	font-size: 16px;
	font-weight: 400;
	line-height: 1.5;
	text-align: center;
	position: relative;
	box-sizing: border-box;
	transition: box-shadow 0.1s ease-in-out;
	background: #fff;
	color: #666;
	box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
	display: inline-block;
	width: 80%;
	max-width: 25em;
	height: 24em;
	margin-top: 10%;
	padding: 3em 1em 3em 1em;
}

#login-btn {
	overflow: visible;
	font: inherit;
	display: inline-block;
	box-sizing: border-box;
	padding: 0 30px;
	vertical-align: middle;
	font-size: 0.875rem;
	line-height: 38px;
	text-align: center;
	text-decoration: none;
	text-transform: uppercase;
	transition: 0.1s ease-in-out;
	transition-property: color, background-color, border-color;
	background-color: #1e87f0;
	color: #fff;
	border: 1px solid transparent;
	cursor: pointer;
	width: 35%;
	min-width: 10em;
	margin: auto;
	margin-top: 4em;
	box-shadow: 0px 4px 4px #0002;
}

#login-subtitle {
	display: block;
	margin: auto;
	margin-top: 0.5em;
	font-weight: 400;
	font-size: 0.75em;
	text-align: center;
	opacity: 0.5;
}

#login-logo {
	display: block;
	width: 20em;
	max-width: 75%;
	margin: auto;
}
