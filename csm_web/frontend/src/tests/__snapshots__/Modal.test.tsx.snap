// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Modal should close correctly when an external modal closer is clicked 1`] = `
<DocumentFragment>
  <div
    class="modal-overlay"
  />
  <div
    class="modal "
  >
    <div
      class="modal-close-container"
    >
      <button
        aria-label="close"
        class="modal-close-x"
      >
        <x-svg
          classname="icon"
        />
      </button>
    </div>
    <div
      class="modal-contents test-modal"
    >
      <div>
        Hello World
      </div>
      <div
        class="modal-close"
      >
        Close
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`Modal should render correctly 1`] = `
<DocumentFragment>
  <div
    class="modal-overlay"
  />
  <div
    class="modal "
  >
    <div
      class="modal-close-container"
    >
      <button
        aria-label="close"
        class="modal-close-x"
      >
        <x-svg
          classname="icon"
        />
      </button>
    </div>
    <div
      class="modal-contents "
    >
      <div>
        Hello World
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`Modal should render correctly with className 1`] = `
<DocumentFragment>
  <div
    class="modal-overlay"
  />
  <div
    class="modal "
  >
    <div
      class="modal-close-container"
    >
      <button
        aria-label="close"
        class="modal-close-x"
      >
        <x-svg
          classname="icon"
        />
      </button>
    </div>
    <div
      class="modal-contents test-modal"
    >
      <div>
        Hello World
      </div>
    </div>
  </div>
</DocumentFragment>
`;
