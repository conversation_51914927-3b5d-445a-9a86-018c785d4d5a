// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`StudentDropper should close modal correctly 1`] = `
<DocumentFragment>
  <span
    class="student-dropper "
  >
    <x-svg
      classname="icon inline-plus-sign"
      title="Drop student from section"
    />
  </span>
</DocumentFragment>
`;

exports[`StudentDropper should render correctly without interaction 1`] = `
<DocumentFragment>
  <span
    class="student-dropper "
  >
    <x-svg
      classname="icon inline-plus-sign"
      title="Drop student from section"
    />
  </span>
</DocumentFragment>
`;

exports[`StudentDropper should render modal correctly after clicking x 1`] = `
<DocumentFragment>
  <span
    class="student-dropper ban-prompt-visible"
  >
    <x-svg
      classname="icon inline-plus-sign"
      title="Drop student from section"
    />
    <div
      class="_mock-modal"
    >
      <button>
        Close
      </button>
      <div>
        <h2
          class="student-dropper-head-item"
        >
          DROP Student
        </h2>
        <div
          class="student-dropper-checkbox-container"
        >
          <input
            id="drop"
            name="drop"
            type="checkbox"
          />
          <label
            class="student-dropper-checkbox-label"
            for="drop"
          >
            I would like to DROP Test Student from this section.
          </label>
          <br />
        </div>
      </div>
      <div>
        <h2
          class="student-dropper-head-item"
        >
          BAN Student
        </h2>
        <div
          class="student-dropper-checkbox-container"
        >
          <input
            disabled=""
            id="ban"
            name="ban"
            type="checkbox"
          />
          <label
            class="student-dropper-checkbox-label"
            for="ban"
          >
            I would like to BAN Test Student from this course.
          </label>
          <br />
        </div>
      </div>
      <div
        class="student-dropper-submit-wrapper"
      >
        <button
          class="danger-btn"
          disabled=""
        >
          Submit
        </button>
      </div>
    </div>
  </span>
</DocumentFragment>
`;
