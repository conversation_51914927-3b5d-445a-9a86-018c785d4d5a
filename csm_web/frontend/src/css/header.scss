/* Header styling */

@use "base/variables" as *;

/// container separating left and right groups of header items
header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30px 2rem;
  margin: 8px -8px 0;
  box-shadow: 0 4px 4px rgb(188 188 188 / 25%);
}

/// individual groups of site title items
.site-title-group {
  display: flex;
  gap: 20px;
  align-items: center;
}

#logo {
  height: 3em;
  margin-top: -14px;
}

.site-title {
  margin-bottom: 0;
  font-size: 23px;
  font-weight: 600;
  line-height: 18px;
  user-select: none;
}

.site-subtitle {
  margin-bottom: 0;
  font-size: 18px;
  font-weight: 600;
  user-select: none;
}

.site-title-link,
.site-subtitle-link {
  color: #bbb;
  text-decoration: none;

  &.is-active {
    color: #808080;
  }
}

#user-profile-pic {
  box-sizing: border-box;
  border: 2px solid #fff;
  border-radius: 26px;
  box-shadow: 0 4px 4px rgb(136 136 136 / 25%);
}

#logout-btn {
  margin: auto 5px auto 30px;
}

#logout-btn .icon {
  vertical-align: middle;
  fill: #808080;
}

#logout-btn:hover .icon {
  fill: #5e5e5e;
}

/* smaller screens */
@media screen and (width <= 500px) {
  header {
    flex-direction: column;
    align-items: center;
  }

  #logo {
    margin-right: 0;
    margin-bottom: 10px;
  }

  .site-title {
    margin: 10px 0;
  }

  .site-title-link {
    margin-right: 0;
  }

  .site-subtitle-link {
    margin: 0 auto;
  }

  #logout-btn {
    margin: 0 auto auto;
  }
}

.page-title {
  // remove bottom margin; top margin removed by base style
  margin-bottom: 0;
  font-size: 24px;
  font-style: normal;
  font-weight: bold;
  color: #646464;
}

.center-title {
  text-align: center;
}
