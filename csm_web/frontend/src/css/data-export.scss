/* Data export styles */
@use "base/variables" as *;

.data-export-container {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: center;

  padding-right: 16px;
}

.data-export-body {
  display: flex;
  flex-direction: row;

  gap: 40px;
}

.data-export-sidebar {
  min-width: 150px;
}

.data-export-content {
  flex: 1;
}

.export-selector-container {
  display: flex;
  flex-direction: column;
  gap: 50px;

  padding-left: 24px;
  margin-top: 16px;
}

.export-selector-footer {
  display: flex;
  align-items: center;
  justify-content: center;
}

.export-page-sidebar-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.export-page-sidebar-title {
  margin-bottom: 8px;
}

.export-selector-data-type-options {
  display: flex;
  flex-direction: column;
  gap: 24px;
  align-items: flex-start;
  justify-content: center;

  width: fit-content;
}

.export-page-courses-options {
  width: fit-content;
  max-height: 25%;
}

.export-selector-data-type-label {
  display: block;
  width: 100%;

  font-size: 1.1rem;

  white-space: nowrap;

  cursor: pointer;
  user-select: none;

  &.active {
    color: $csm-green-darkened;
  }
}

.export-page-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: stretch;
}

.export-page-config {
  display: flex;
  flex-flow: row wrap;
  gap: 8px;
}

.export-page-sidebar {
  min-width: 650px;
  padding: 16px 0;
  background-color: #f3f3f3;
  border-radius: 12px;
}

.export-page-sidebar.sidebar-left {
  flex: 1;
}

.export-page-sidebar.sidebar-right {
  flex: 2;
}

.export-page-header {
  display: flex;
  gap: 16px;
  align-items: center;
  justify-content: flex-start;
}

.export-page-footer {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}

.export-page-preview {
  flex: 2;
}

.export-page-preview-container {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.export-page-preview-title {
  // make margin smaller
  margin-bottom: 8px;
}

.export-preview-header {
  display: flex;
  flex-direction: row;
  gap: 8px;
  align-items: center;

  margin-bottom: 8px;
}

.export-preview-select {
  width: 75px !important;
}

.export-preview-icon {
  color: black;
  cursor: pointer;
}

.export-preview-icon:hover {
  color: #222;
}

.export-preview-refresh-tooltip-container {
  position: relative;

  .tooltip-body {
    // offset tooltip slightly
    margin-left: 8px;
  }
}

.export-preview-table-container {
  max-width: 80vw;
  overflow-x: auto;
}

.export-page-preview-wrapper {
  padding: 0 12px;
}

.export-preview-table {
  border-collapse: collapse;
}

.export-preview-table-item,
.export-preview-table-header-item {
  padding: 4px;

  white-space: nowrap;
  border: 1px #aaa solid;
}

.export-preview-table-header-item {
  text-align: left;
}

.export-preview-table-more-row-item {
  padding: 8px 0;
  color: #888;

  column-span: all;
  text-align: left;
}
