@use "base/variables" as *;

#matcher-contents {
  display: flex;
}

#matcher-sidebar {
  display: flex;
  flex-flow: column wrap;
  gap: 10px;
  width: fit-content;
  padding: 40px 30px 0 20px;
}

#matcher-body {
  width: 100%;
}

#matcher-main {
  width: 90%;
}

#matcher-main input[type="time"]::-webkit-calendar-picker-indicator {
  padding: 1px;

  /* display: none; */
  margin-left: 0;
}

/*
#matcher-header {
}
*/

.matcher-title {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.matcher-stages {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-around;
  max-width: 850px;
  margin: 20px auto;
  user-select: none;
}

.matcher-stages-stage {
  width: fit-content;
}

.matcher-stages-stage.active-stage {
  color: $csm-green;
}

.matcher-link {
  padding: 15px 20px;
  color: #000;
  text-decoration: none;
  background-color: $csm-green-lightened;
  border-radius: 10px;
}

.matcher-link.active {
  /* color: $csm-green; */
  font-weight: bold;
  background-color: $csm-green;
}

.matcher-body {
  display: flex;
  flex-direction: row;
  overflow-x: auto;
}

.matcher-body-footer,
.matcher-body-footer-right,
.matcher-body-footer-sticky {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-top: 20px;
}

.matcher-body-footer {
  justify-content: space-between;
}

.matcher-body-footer-right {
  justify-content: flex-end;
}

.matcher-body-footer-sticky {
  position: sticky;
  bottom: 0;
  padding: 25px 0 15px;
  margin-top: 0;
  background: linear-gradient(to bottom, #fff9 0%, #fff 50%);

  /* fallback for gradient */
  background-color: #fffc;
}

.matcher-body-footer-status-container {
  display: flex;
  flex-direction: row;
  gap: 4px;
  align-items: center;
  justify-content: center;
}

.matcher-body-footer-status-icon {
  width: 2em !important;
  height: 2em !important;
}

.mentor-sidebar-left,
.coordinator-sidebar-left {
  display: flex;

  /* width: 20vw; */
  flex: 0.4;
  flex-direction: column;
}

.mentor-sidebar-right,
.coordinator-sidebar-right {
  flex: 1;
}

.matcher-sidebar-left-top {
  flex: 1;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 5px;
}

.matcher-sidebar-left-bottom {
  display: flex;
  flex-direction: column;
  gap: 3px;
  align-items: center;
  padding: 15px;
}

.matcher-sidebar-left-bottom-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 15px;
}

.matcher-submit-status-container {
  position: relative;
}

.matcher-submit-status-icon {
  position: absolute !important;
  top: -0.75em;
  left: 10px;
  width: 1.5em !important;
  height: 1.5em !important;
}

.matcher-submit-status-icon.error {
  color: $csm-danger;
}

.matcher-detail-time {
  font-size: 0.9rem;
}

.matcher-detail {
  font-size: 0.9rem;
}

/* Modal */

.matcher-calendar-modal {
  width: 800px !important;
  height: 700px !important;
}

.matcher-calendar-modal-contents {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: center;
  width: 100%;
  height: calc(100% - 40px);
}

.matcher-calendar-modal-header {
  margin: 0 20px 15px;
  font-weight: bold;
}

.matcher-confirm-modal-header {
  text-align: center;
}

.matcher-confirm-modal-content {
  max-height: 60vh;
  overflow-y: auto;
}

.matcher-confirm-modal-list {
  column-count: 2;
}

.matcher-confirm-modal-empty-list-text {
  margin-left: 1em;
}

.matcher-confirm-modal-list-item {
  margin-right: 16px;
  margin-bottom: 5px;
  list-style-position: outside;
}

.matcher-confirm-modal-buttons {
  display: flex;
  flex-direction: row;
  gap: 16px;
  justify-content: center;

  margin-top: 16px;
}

/* Table */

.matcher-table-sort-group {
  display: flex;
  flex-direction: row;
  gap: 3px;
  cursor: pointer;
  user-select: none;
}

.matcher-table-sort-icon {
  width: 1em;
  margin-top: 2px;
}

/* Mentor Preferences */

.matcher-sidebar-pref-help {
  margin: 20px 0;
  font-style: normal;
}

/* CreateStage.tsx */

.matcher-sidebar-create,
.matcher-sidebar-selected,
.matcher-sidebar-tiling {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.matcher-sidebar-selected-bottom,
.matcher-sidebar-tiling-bottom {
  display: flex;
  flex-direction: row;
  gap: 8px;
  align-items: center;
  justify-content: center;
  padding-top: 15px;
}

.matcher-sidebar-create-bottom,
.matcher-sidebar-selected-bottom {
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: center;
  justify-content: center;
  padding-top: 15px;
}

.matcher-sidebar-create-bottom-row,
.matcher-sidebar-selected-bottom-row {
  display: flex;
  flex-direction: row;
  gap: 8px;
  align-items: center;
  justify-content: center;
}

.matcher-sidebar-create-footer,
.matcher-sidebar-selected-footer {
  display: flex;
  flex: 1;
  flex-direction: column;
  justify-content: flex-end;
  font-size: 0.95rem;
  font-style: italic;
}

.matcher-created-time-container {
  display: flex;
  flex-direction: row;
}

.matcher-selected-time-container {
  list-style: disc outside;
}

.matcher-created-times,
.matcher-selected-times {
  padding-left: 10px;
  margin-bottom: 10px;
  font-size: 0.95rem;
}

.matcher-selected-times {
  margin-top: 0;
  margin-left: 15px;
}

.matcher-selected-time {
  position: relative;
  left: -3px;
}

.matcher-created-time {
  display: flex;
  flex-direction: row;
  gap: 8px;
  align-items: center;
  padding: 5px 0;
}

.matcher-created-time-remove {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-right: 5px;
}

.matcher-created-time-day-input {
  width: fit-content;
  padding-right: 25px;
}

.matcher-sidebar-header {
  padding-bottom: 15px;
  font-weight: bold;
}

.matcher-remove-time-icon {
  position: relative;
  top: 1px;
  color: $csm-danger;
  cursor: pointer;
}

.matcher-remove-time-icon:hover {
  color: $csm-danger-darkened;
}

.matcher-tiling-subheader,
.matcher-sidebar-subheader {
  /* font-style: italic; */
  font-weight: 600;
}

.matcher-tiling-day-form {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-left: 10px;
}

.matcher-sidebar-tiling-body {
  font-size: 0.95rem;
}

.matcher-tiling-range-container,
.matcher-tiling-length-container,
.matcher-tiling-day-container {
  padding: 5px 0;
}

.matcher-tiling-range-input-container {
  display: flex;
  flex-direction: row;
  gap: 8px;
  align-items: center;
  margin-top: 4px;
}

.matcher-tiling-length-input-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-top: 4px;
}

.matcher-tiling-length-input {
  width: 75px;
  margin-right: 5px;
}

.matcher-tooltip-container {
  position: relative;
}

.matcher-tooltip-info-icon {
  width: 1em;
  margin-left: 3px;
}

.matcher-tooltip-info-icon:hover {
  color: #888;
}

.matcher-tiling-link-day-label {
  white-space: nowrap;
}

.matcher-tiling-link-day-container {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  margin-top: 5px;
}

.matcher-tiling-tooltip-body {
  width: fit-content;
  max-width: 250px;
}

.matcher-tiling-metadata-container,
.matcher-sidebar-metadata-container {
  padding-top: 5px;
}

.matcher-description-text {
  max-width: 350px;
  margin-top: 5px;
  margin-left: 5px;

  overflow-wrap: break-word;
}

.matcher-unsaved-changes-container {
  height: fit-content;
  margin-right: 15px;
}

.matcher-unsaved-changes {
  font-style: italic;
}

/* ReleaseStage.tsx */

.mentor-list-container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.mentor-list-top {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.mentor-list-top-search {
  display: flex;
  flex-direction: row;
  gap: 15px;
  align-items: center;
  justify-content: space-between;
}

.mentor-list-num-submitted {
  font-size: 0.9rem;
}

.mentor-list-top-buttons {
  display: flex;
  flex-direction: row;
  gap: 8px;
  align-items: center;
  justify-content: space-between;
}

.mentor-list-header {
  display: flex;
  flex-direction: row;
  padding: 10px 30px 10px 15px;
  margin-top: 15px;
  font-size: 0.9rem;
  font-weight: bold;
  background-color: $csm-green;
}

.mentor-list {
  display: flex;
  flex-direction: column;
  gap: 3px;
  padding: 15px 30px 15px 15px;
  background-color: #f5f5f5;
}

.mentor-list-with-preferences,
.mentor-list-without-preferences {
  flex: 1;
}

.mentor-list-item {
  display: flex;
  flex-direction: row;
  margin: 2px 0;
}

.mentor-list-item-name {
  flex: 0.9;
  flex-basis: 0;
}

.mentor-list-item-email {
  flex: 1;
  flex-basis: 0;
  font-size: 0.9rem;
}

.mentor-list-header .mentor-list-item-email {
  font-size: inherit;
}

.mentor-list-item-check {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 10%;
  min-width: 3em;
}

.mentor-list-item-undo-remove,
.mentor-list-item-remove,
.mentor-list-item-view {
  margin-top: 2px;
  margin-right: 5px;
}

.mentor-list-icon {
  width: 1rem;
  height: 1rem;
}

.mentor-list-item-remove {
  color: $csm-danger;
}

.mentor-list-item-remove:hover {
  color: $csm-danger-darkened;
  cursor: pointer;
}

.mentor-list-item-undo-remove {
  color: black;
}

.mentor-list-item-undo-remove:hover {
  color: #555;
  cursor: pointer;
}

.mentor-list-right {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 15px;
  border-radius: 5px;
}

.mentor-list-item-selected .mentor-list-item-view {
  color: $csm-green-darkened;
  user-select: none;
}

.mentor-list-item-unclickable .mentor-list-item-view {
  color: #888;
  user-select: none;
}

.mentor-list-item-clickable .mentor-list-item-view:hover {
  color: $csm-green;
  cursor: pointer;
}

.mentor-list-item-removed {
  color: $csm-neutral;
  text-decoration: line-through;
}

.mentor-add-textarea {
  width: calc(min(350px, 50vw));
  height: calc(min(200px, 50vh));
  margin-top: 10px;
  margin-bottom: 10px;
  margin-left: 10px;
  resize: none;
}

.release-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.matcher-pref-color-unavailable {
  color: #888;
}

.matcher-pref-color-best {
  color: green;
}

.matcher-pref-modal-email {
  font-size: 0.9rem;
  font-weight: normal;
}

.matcher-pref-input-label {
  display: flex;
  flex-direction: row;
  gap: 8px;
  align-items: center;
  margin-top: 8px;
  font-weight: bold;
}

/* ConfigureStage.tsx */

.matcher-mentor-min-max {
  font-size: 0.8rem;
}

.matcher-configure-sidebar-contents {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.matcher-configure-sidebar-header {
  margin-bottom: 10px;
  font-weight: bold;
}

.matcher-configure-input-container {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: space-between;
}

.matcher-configure-input-group {
  display: flex;
  flex: 1;
  flex-direction: column;
  margin-bottom: 8px;
}

.matcher-configure-input {
  width: 80%;
}

.matcher-configure-sidebar-buttons {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin: 15px 0;
}

.matcher-configure-sidebar-footer {
  display: flex;
  flex: 1;
  flex-direction: column;
  justify-content: flex-end;
  font-size: 0.95rem;
  font-style: italic;
}

.matcher-configure-error {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.matcher-configure-error-text {
  font-size: 0.9rem;
  font-style: italic;
  color: $csm-danger;
}

.matcher-configure-error-icon {
  width: 1.25em;
  height: 1.25em;
  color: $csm-danger;
}

/* EditStage.tsx */

.matcher-assignment-above-head {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.matcher-assignment-search {
  width: 50%;
  margin-bottom: 15px;
}

.matcher-assignment-mentor-head {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 10px 0;
  margin-bottom: 5px;
  font-size: 0.9rem;
  font-weight: bold;
  background-color: $csm-green;
}

.matcher-assignment-row {
  display: flex;
  padding: 10px 0;
  border-bottom: 1px solid $csm-neutral;
}

.matcher-assignment-row.unassigned {
  background-color: #fdd;
}

.matcher-assignment-mentor-empty {
  font-style: italic;
}

.matcher-assignment-mentor-select {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 25px;
  margin-right: 2px;
}

.matcher-assignment-mentor-info {
  display: flex;
  flex: auto;
  flex-direction: column;
  justify-content: center;
  width: 25%;
  margin-right: 5px;
}

.matcher-assignment-mentor-name {
  overflow-x: hidden;
  text-overflow: ellipsis;
}

.matcher-assignment-mentor-email {
  overflow-x: hidden;
  font-size: 0.9rem;
  font-style: italic;
  color: #888;
  text-overflow: ellipsis;
}

.matcher-assignment-section-times {
  display: flex;
  flex: auto;
  flex-direction: row;
  width: 15%;
  margin-right: 5px;
  font-size: 0.95rem;
}

.matcher-assignment-section-times-edit {
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 18px;
  min-width: 18px;
  padding-right: 5px;
}

.matcher-assignment-section-times-edit-icon {
  width: 100%;
  height: 100%;
  color: $csm-neutral;
  user-select: none;
}

.matcher-assignment-section-times-edit-icon:hover {
  color: $csm-green;
  cursor: pointer;
}

.matcher-assignment-section-times-data {
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow-x: hidden;
}

.matcher-assignment-section-times-input {
  width: initial;
  max-width: initial;
}

.matcher-assignment-section-times-input:focus {
  border-color: #888;
  outline-color: #888;
}

.matcher-assignment-section-times-option {
  font-family: inherit;
  font-size: 0.9rem;
}

.matcher-assignment-section-capacity {
  width: 85px;
  margin-right: 5px;
}

.matcher-assignment-section-description {
  width: 25%;
}

.matcher-assignment-section-capacity,
.matcher-assignment-section-description {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.matcher-assignment-section-capacity-input,
.matcher-assignment-section-description-input {
  width: calc(100% - 20px);
}

.matcher-assignment-section-capacity-input:focus,
.matcher-assignment-section-description-input:focus {
  outline-color: #888;
}

.matcher-assignment-burger {
  width: 15px;
  height: 15px;
  cursor: pointer;
}

.matcher-assignment-group-hover {
  background-color: #eee;
}

.matcher-assignment-mentor-dragging {
  color: $csm-neutral;
}

.matcher-assignment-footer-help {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.matcher-assignment-footer-help-icon,
.matcher-assignment-footer-error-icon {
  width: 1.25em;
  height: 1.25em;
  margin-right: 2px;
}

.matcher-assignment-footer-help-text,
.matcher-assignment-footer-error-text {
  font-size: 0.9rem;
  font-style: italic;
}

.matcher-assignment-footer-error-icon,
.matcher-assignment-footer-error-text {
  color: $csm-danger;
}

.matcher-assignment-button-div {
  display: flex;
  flex-direction: row;
  column-gap: 15px;
}

.matcher-assignment-save-button {
  padding: 8px 20px;
  color: white;
  text-decoration: none;
  background-color: $csm-neutral;
  border: none;
  border-radius: 10px;
}

.matcher-assignment-save-button:hover {
  background-color: $csm-neutral-darkened;
}

.matcher-pref-distribution-div,
.matcher-assignment-distribution-div {
  height: 100%;
}

.matcher-assignment-confirm-modal-contents {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  height: 65%;
  text-align: center;
}

.matcher-assignment-confirm-modal-footer {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-evenly;
  width: 100%;
}
