/// Styles for the section view.
/// These styles are for both coordinators and students.

@use "base/variables" as *;

/// local variables
$attendance-outer-radius: 20px;
$attendance-tab-radius: 10px;

.override-label {
  width: fit-content;
  padding: 3px 5px;
  margin: 10px 0;
  font-size: 15px;
  color: white;
  background-color: #ffd874;
  border-radius: 4px;
}

.section-detail-info-card-contents {
  position: relative;
  min-width: 12em;
  border: 2px solid #e7e7e7;
  border-radius: $attendance-outer-radius;
  box-shadow: 0 4px 4px rgb(198 198 198 / 25%);
}

/// only add padding if specified,
/// or if it's the mentor attendance table
.section-detail-info-card-contents.padded-card,
#mentor-attendance-content {
  padding: 40px;
}

.section-detail-info-card-contents h5 {
  margin: 0 auto 0.5em;
  font-size: 25px;
  color: #727070;
}

.section-detail-info-card-contents .location-link {
  margin: 0 auto 0.5em;
  font-size: 25px;
}

.section-detail-info-card-contents.mentor {
  text-align: center;
}

.section-detail-info-card-contents a {
  word-wrap: break-word;
}

#section-detail-sidebar {
  display: flex;
  flex-flow: column wrap;
  width: fit-content;
}

#section-detail-sidebar a {
  margin: 0 100px 20px 0;
  font-size: 18px;
  color: #4b4b4b;
  text-decoration: none;
}

#section-detail-sidebar a.active {
  color: $csm-green;
}

#section-detail-body * {
  max-width: 90vw;
}

.drop-confirmation {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.drop-confirmation h5 {
  margin: 0 auto 0.5rem;
  font-size: 25px;
  color: #727070;
}

.drop-confirmation p {
  margin-top: 0;
  font-size: 14px;
  color: #bfbfbf;
}

#section-detail-body {
  display: flex;
}

@media screen and (width <= 1100px) {
  /* only wrap for medium/small screens */
  #section-detail-body {
    flex-wrap: wrap;
  }
}

#students-table .inline-plus-sign {
  position: relative;
  display: inline-block;
  color: $csm-danger;
  cursor: pointer;
}

#students-table .csm-table-item {
  display: flex;
  flex-direction: row;
  gap: $button-icon-gap;
  align-items: center;
  justify-content: flex-start;
  padding: 20px 30px;
}

.should-ban-prompt {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  width: 10em;
  padding: 10px;
  font-size: 0.8em;
  text-align: center;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px 0 rgb(0 0 0 / 15%);
  transform: translateX(-50%);
}

.should-ban-prompt::before {
  position: absolute;
  right: 0;
  bottom: calc(-1 * $arrow-size);
  left: 0;
  display: block;
  width: 0;
  height: 0;
  margin: 0 auto;
  content: "";
  border-top: $arrow-size solid white;
  border-right: $arrow-size solid transparent;
  border-left: $arrow-size solid transparent;
}

.should-ban-prompt .ban-cancel {
  position: absolute !important;
  top: -5px;
  right: 3px;
  font-size: 1.5rem;
  color: #dedede !important;
}

.should-ban-prompt .btn-group {
  display: flex;
  justify-content: space-evenly;
  margin-top: 0.3em;
}

.should-ban-prompt .btn-group button {
  cursor: pointer;
  border: unset;
  border-radius: 5px;
}

.should-ban-prompt .btn-group button.yes {
  background-color: $csm-danger;
}

.should-ban-prompt .btn-group button.yes:hover {
  background-color: $csm-danger-darkened;
}

.should-ban-prompt .btn-group button.no {
  background-color: $csm-neutral;
}

.should-ban-prompt .btn-group button.no:hover {
  background-color: #aeaeae;
}

#students-table .csm-form {
  display: flex;
  padding-left: 0;
}

#students-table .csm-form input[type="submit"] {
  position: relative;
  top: unset;
  bottom: 3px;
  display: inline-block;
  padding: 0;
  margin: auto;
  color: $csm-green;
  background-color: inherit;
  transform: none;
}

#students-table .csm-form input[type="submit"]:hover {
  color: $csm-green-darkened;
}

#students-table .csm-form input {
  margin: auto;
}

#students-table .inline-plus-sign:hover {
  color: $csm-danger-darkened;
}

#copy-student-emails {
  margin-right: 8px;
  cursor: pointer;
}

#copy-student-emails:hover {
  color: #9c9c9c;
}

#copy-student-emails-success {
  position: absolute;
}

/* Section details */

.section-detail-header .relation-label {
  padding: 5px 14px;
  margin-bottom: 40px;
  font-size: 14px;
  border-radius: 4px;
}

.section-detail-header-title {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20px;
}

.section-detail-header-title > h3 {
  margin: 0 20px 0 0;
  font-size: 30px;
  line-height: 30px;
}

.section-detail-header-title > h4 {
  margin: 0;
  font-size: 25px;
  font-weight: normal;
  color: #bfbfbf;
}

.section-detail-page-title {
  margin: 0 0 45px;

  // color: #646464;
  font-size: 30px;
  font-weight: bold;
}

.section-info-cards-container {
  display: flex;
  flex-wrap: wrap;
}

.section-info-cards-right {
  display: flex;
  flex: 1;
  flex-wrap: wrap;
}

.section-detail-info-card {
  margin: 0 50px 50px 0;

  /// only direct children
  > {
    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      color: $csm-content-title-color;
    }
  }
}

// .section-detail-info-card h4 {
//   color: #646464;
//   font-size: 25px;
//   font-weight: bold;
//   margin: 0 0 20px;
// }

.section-detail-info-card.drop-section {
  display: flex;
  flex-basis: 100%;
  text-align: center;
}

.section-detail-info-card-contents .divider {
  margin: 40px -40px;
  border-top: inherit;
}

.section-detail-info-card-contents.drop-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  border-color: #ffadad;
}

%info-card-action {
  font-size: 14px;

  // position: absolute;
}

.info-card-actions {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin: -30px -30px 10px;
}

.info-card-edit-btn {
  @extend %info-card-action;

  top: 10px;
  right: 10px;
}

.override-info-card-edit-btn {
  @extend %info-card-action;

  top: 170px;
  right: 10px;
}

.delete-spacetime-btn {
  @extend %info-card-action;

  top: 10px;
  left: 10px;
}

.delete-override-btn {
  position: absolute;
  top: 170px;
  left: 10px;
}

.meta-field {
  font-weight: 600;
  color: #727070;
}

.spacetime-edit-modal {
  height: 60vh;
  padding: 0 20px;
}

/// Attendance table

.mentor-attendance-page {
  display: flex;
  flex-wrap: wrap;
  gap: 25px;
  align-items: flex-start;
}

#mentor-attendance {
  display: flex;
  flex-direction: row;

  border-radius: 0 $attendance-outer-radius $attendance-outer-radius $attendance-tab-radius;
  box-shadow: 4px 4px 4px rgb(198 198 198 / 25%);
}

#mentor-attendance-content {
  max-width: 500px;
  padding-top: 0;
  padding-bottom: 30px;
  overflow: hidden;
  border: 2px solid #e7e7e7;
  border-radius: 0 $attendance-outer-radius $attendance-outer-radius 0;
}

#attendance-side-bar {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border-radius: $attendance-tab-radius 0 0 $attendance-tab-radius;
}

#attendance-calendar-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
  color: #5e5e5e;
  cursor: pointer;
}

#attendance-calendar-toggle:hover {
  background-color: #fafafa;
}

#attendance-calendar-toggle.active {
  background-color: #dedede;
}

#attendance-calendar-toggle.active:hover {
  background-color: #e8e8e8;
}

#attendance-date-tabs-container {
  display: flex;
  flex-basis: 0;
  flex-direction: column;
  flex-grow: 1;
  margin: 0;
  overflow: hidden scroll;
}

#attendance-date-tabs-container > div {
  padding: 15px 30px;
  font-size: 15px;
  color: #5e5e5e;
  cursor: pointer;
  background-color: #f0f0f0;
  border: 1px solid #e9e9e9;
  border-right: 0;
  border-radius: $attendance-tab-radius 0 0 $attendance-tab-radius;
}

#attendance-date-tabs-container > div:hover {
  background-color: #dedede;
}

#attendance-date-tabs-container > div.active {
  background-color: white;
}

#mentor-attendance-table {
  width: 100%;
}

#mentor-attendance-controls {
  display: flex;
  flex-direction: row;
  gap: 15px;
  justify-content: flex-end;
  max-width: 780px;
  margin-top: 25px;
}

.mentor-attendance-input {
  text-align-last: center;
}

/// student attendance table status
.attendance-status {
  width: 200px;
  padding: 5px 10px;
  color: white;
  text-align: center;
  border-radius: 6px;
}

/// attendance calendar content
.calendar-warning-icon {
  color: $csm-danger;
}

.calendar-warning-icon-tooltip {
  z-index: 5;
}
