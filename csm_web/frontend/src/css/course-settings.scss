@use "base/variables" as *;

.course-settings-btn {
  display: block;
  margin: 0 5px;
  font-size: 16px;
  cursor: pointer;
  border: none;
}

.course-settings-title {
  text-align: center;
}

.course-settings-content {
  display: flex;
  flex-direction: column;
  height: 90%;
}

.course-settings-container {
  display: flex;
  flex: 1;
  flex-direction: column;
  padding: 0 25px;
}

.course-settings-row {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.course-settings-label-container {
  display: flex;
  flex: 1;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}

.course-settings-tooltip-container {
  position: relative;
  margin-left: 5px;
}

.course-settings-tooltip-info-icon {
  width: 1em;
  color: #888;
}

.course-settings-tooltip-info-icon:hover {
  color: #aaa;
}

.course-settings-input-container {
  display: flex;
  flex: 1;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
}

.course-settings-input {
  width: 50%;
  font-family: inherit;
}

.course-settings-input-post {
  margin-left: 5px;
}

.course-settings-footer {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin-top: 25px;
}
