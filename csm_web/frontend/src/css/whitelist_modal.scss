@use "base/variables" as *;

.whitelist-modal-container {
  width: 65vw !important;
  height: 65vh !important;
}

.whitelist-modal {
  height: 90%;
}

.whitelist-modal-add-container,
.whitelist-modal-delete-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16px;
}

.whitelist-modal-subtitle {
  margin-bottom: 3px;
  font-size: 1.1rem;
  font-weight: bold;
}

.whitelist-modal-description {
  font-size: 0.95rem;
  color: #333;
}

.whitelist-modal-submit-status {
  position: relative;
}

.whitelist-modal-submit-status * {
  position: absolute !important;
  top: 4px;
  left: 10px;
  width: 1.5em !important;
  height: 1.5em !important;
}

.whitelist-modal-footer {
  display: flex;
  flex-direction: row;
  align-content: center;
  justify-content: center;
}

/* Add tab */

.whitelist-modal-add-textarea {
  width: calc(min(450px, 50vw));
  height: calc(min(300px, 50vh));
  margin: 8px 0;
  resize: none;
}

/* Delete tab */

.whitelist-modal-delete-search-container {
  display: flex;
  flex-direction: row;
  gap: 4px;
  align-items: center;
  justify-content: center;
}

.whitelist-modal-delete-search {
  margin-top: 4px;
}

.whitelist-modal-delete-search-description {
  font-size: 0.95rem;
  color: #333;
}

.whitelist-modal-delete-content {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  min-width: 30vw;
  max-width: 50vw;
  max-height: 40vh;
  margin: 8px 0;
  overflow: auto;
}

.whitelist-modal-delete-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 50%;
  min-width: 400px;
  padding: 2px 0;
  margin: 2px 0;
}

.whitelist-modal-delete-row.deleted {
  color: $csm-neutral;
  text-decoration: line-through;
  background-color: #eee;
  border-radius: 3px;
}

.whitelist-modal-delete-row-content {
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
}

.whitelist-modal-delete-row-btn {
  min-width: 24px;
  color: $csm-danger;
  cursor: pointer;
  user-select: none;
}

.whitelist-modal-delete-row.deleted .whitelist-modal-delete-row-btn {
  color: #aaa;
}

.whitelist-modal-delete-row-email {
  flex: 2;
  font-size: 0.9rem;
  color: #888;
}

.whitelist-modal-delete-row-name {
  flex: 1;
}
