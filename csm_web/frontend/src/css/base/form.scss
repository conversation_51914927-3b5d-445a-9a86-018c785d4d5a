/// Form styles

@use "variables" as *;

.csm-form {
  display: flex;
  flex-direction: column;
  gap: 20px;

  padding: 0 40px;
}

.form-select,
.form-input,
.form-date {
  box-sizing: content-box;
  display: block;

  font-family: inherit;

  background-color: #f5f5f5;
  border: none;
  border-radius: 6px;
}

.form-label {
  display: flex;
  flex-direction: column;
  gap: 5px;
  align-items: flex-start;

  font-weight: bold;
}

// height/padding differences
.form-input,
.form-select {
  height: 16px;
  padding: 8px;
}

.form-date {
  height: 20px;
  padding: 6px 8px;
}

// light version
.light {
  &.form-input,
  &.form-date,
  &.form-select {
    background-color: white;
  }
}

/// Select element

.form-select {
  width: 200px;
  max-width: 200px;
  appearance: none;

  background-image: url('data:image/svg+xml;utf8,<svg aria-hidden="true" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M207.029 381.476L12.686 187.132c-9.373-9.373-9.373-24.569 0-33.941l22.667-22.667c9.357-9.357 24.522-9.375 33.901-.04L224 284.505l154.745-154.021c9.379-9.335 24.544-9.317 33.901.04l22.667 22.667c9.373 9.373 9.373 24.569 0 33.941L240.971 381.476c-9.373 9.372-24.569 9.372-33.942 0z"/></svg>');
  background-repeat: no-repeat, repeat;
  background-position:
    right 0.7em top 50%,
    0 0;
  background-size:
    1em auto,
    100%;
  border: none;
}

/* Neccessary for options to be legible on Windows */
.form-select > option {
  color: black;

  background-color: white;
}

// Disabled input/select elements
.form-select:disabled,
.form-input:disabled {
  cursor: not-allowed;
  background-color: #fafafa;
}

/// Radio element

.form-radio {
  display: inline-block;
  width: 16px;
  height: 16px;
  padding: 0;
  margin: 0 10px 0 0;

  vertical-align: middle;
  appearance: none;

  border: 2px solid $csm-green;
  border-radius: 8px;

  &[value="true"] {
    position: relative;
    bottom: 1px;
  }

  &:checked {
    background-color: $csm-green;
  }
}

/// Radio input item (box and label)
.form-radio-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}

.form-radio-group {
  /// default should be column
  &,
  &-col {
    display: flex;
    flex-direction: column;
    gap: 5px;
    align-items: flex-start;

    margin: 5px;
  }

  &-row {
    display: flex;
    flex-direction: row;
    gap: 40px;
    align-items: center;
    justify-content: flex-start;

    margin: 5px;
  }
}

/// Action buttons at bottom of form
.form-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
