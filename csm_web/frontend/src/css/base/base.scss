/* Generic global styling */

@use "variables" as *;

html,
body {
  width: 100%;
  height: 100%;

  font-family: $default-font;
}

body {
  max-width: 100%;

  /* Usually set by browser stylesheet, but we standardize it here to account for different browsers */
  margin: 8px;
  overflow-x: hidden;
}

main {
  padding: 2rem;
  margin-left: -8px;
}

button {
  // use parent font instead of default button font
  font-family: inherit;

  // use parent font size instead of default button font size
  font-size: inherit;

  &:hover {
    cursor: pointer;
  }
}

h1,
h2,
h3,
h4,
h5,
h6 {
  // remove top margin for all headers;
  // especially for the modal windows
  margin-top: 0;
}

/// Styling for inline + or x signs, mostly used for add or close buttons.
.inline-plus-sign {
  font-size: 1.5rem;
  font-weight: 600;
  vertical-align: sub;
}

.icon {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  margin-top: -0.125em;
  overflow: visible;

  font-size: inherit;

  fill: currentcolor;
  stroke: currentcolor;
}

// some icons are outline icons that require no fill
.icon.outline {
  fill: none;
}

/// Styling for relation label; used across multiple pages
.relation-label {
  min-width: 60px;
  max-width: fit-content;
  padding: 8px 16px;
  margin-top: auto;

  color: white;
  text-align: center;
  text-transform: capitalize;

  border-radius: 20px;
}
