/* Modal styling */
@use "sass:math" as math;
@use "variables" as *;
@use "button";

/// Overlay for the modal window; grays out the screen behind the modal.
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;

  width: 100%;
  max-width: unset !important;
  height: 100%;

  background: rgb(0 0 0 / 60%);
}

/// Modal container; controls the positioning within the screen.
.modal {
  position: fixed;
  top: 50%;
  left: 50%;
  z-index: 1000;

  width: max-content;
  max-width: 90vw;
  height: max-content;
  max-height: 90vh;

  background-color: white;
  border-radius: 20px;
  transform: translate(-50%, -50%);
}

/// Contents of the modal; all children are put into this container.
.modal-contents {
  padding: math.div($modal-padding-y, 2);
  overflow: auto;
}

/// Top bar in the modal that contains the close button.
.modal-close-container {
  position: sticky;

  display: flex;
  align-items: flex-end;
  justify-content: flex-end;

  height: $modal-close-height;
  margin-right: 1em;
}

.modal-close-x {
  // center the x icon vertically and horizontally
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;

  color: black;
  cursor: pointer;

  user-select: none;

  background-color: inherit;
  border: none;
  outline: none;

  &:hover {
    color: gray;
  }

  .icon {
    width: 1.1rem;
    height: 1.1rem;
  }
}
