/* Styling for buttons and tabs. */

@use "variables" as *;

/** Base button styling */
%button {
  display: flex;
  flex-direction: row;
  gap: $button-icon-gap;
  align-items: center;
  justify-content: center;

  padding: 8px 15px;

  // need to respecify the font family on buttons
  font-family: $default-font;

  // default text
  text-decoration: none;

  // set cursor
  cursor: pointer;

  // don't allow selecting text in buttons
  user-select: none;

  // no border around button
  border: none;

  // rounded corners
  border-radius: 10px;
  outline: none;

  &:disabled,
  &:disabled:hover {
    cursor: not-allowed;
    background-color: $csm-neutral;
  }
}

.primary-btn {
  @extend %button;
  color: white;

  background-color: $csm-green;

  &:not(:disabled):hover {
    background-color: $csm-green-darkened;
  }
}

.secondary-btn {
  @extend %button;
  color: white;

  background-color: $csm-neutral;

  &:not(:disabled):hover {
    background-color: $csm-neutral-darkened;
  }
}

.danger-btn {
  @extend %button;
  color: white;

  background-color: $csm-danger;

  &:not(:disabled):hover {
    background-color: $csm-danger-darkened;
  }
}

.primary-link-btn {
  @extend %button;

  // bolder font
  font-weight: bold;
  color: $csm-green;

  background-color: inherit;

  &:not(:disabled):hover {
    color: $csm-green-darkened;
  }
}

.secondary-link-btn {
  @extend %button;
  color: $csm-neutral;

  background-color: inherit;

  &:not(:disabled):hover {
    color: $csm-neutral-darkened;
  }
}

.primary-outline-btn {
  @extend %button;
  font-weight: bold;
  color: $csm-green;

  background-color: inherit;
  border: 3px solid $csm-green;

  &:not(:disabled):hover {
    color: $csm-green-darkened;

    border-color: $csm-green-darkened;
  }
}

.tab-list {
  // flex box with centered content
  display: flex;
  flex-direction: row;

  // small gap between tabs
  gap: 8px;
  align-items: center;
  justify-content: center;

  // extra margins on top and bottom
  margin-top: 12px;
  margin-bottom: 12px;
}

.tab {
  @extend %button;

  // extra padding for tabs
  padding: 12px 15px;

  color: white;

  background-color: $csm-neutral;

  // active tab styling
  &.active {
    background-color: $csm-green;
  }

  // inactive hover; darken to suggest it is clickable
  // no active hover, since an active tab cannot be clicked
  &:not(.active):hover {
    background-color: $csm-neutral-darkened;
  }
}
