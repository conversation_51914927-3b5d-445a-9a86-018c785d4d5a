/* Home page styling */

@use "base/variables" as *;

.course-card {
  position: relative;
  width: 320px;
  min-width: 320px;
  min-height: 180px;
  border-top: 30px solid;
  border-top-color: $csm-theme-default;
  border-radius: 16px;
  box-shadow: 0 5px 50px rgb(0 0 0 / 15%);
}

.course-card-link {
  color: unset;
  text-decoration: none;
  cursor: pointer;
}

.course-cards-container > .course-card,
.course-card-link {
  margin: 0 75px 50px 0;
}

.course-card-link:hover > .course-card {
  background-color: $csm-link-hover;
}

.course-card-contents {
  display: flex;
  flex-flow: column wrap;
  min-height: 120px;
  padding: 30px;
}

.course-card-contents > *:nth-last-child(2) {
  /* Ensures there is space between the relation label and whatever precedes it */
  margin-bottom: 20px;
}

.course-card-name {
  margin: 0;
  font-size: 24px;
  font-weight: bold;
}

/* Format course card section time lists */
.course-card-section-time:not(:last-of-type)::after {
  display: block;
  font-size: 1.2em;
  content: "&";
}

.course-card-title {
  margin: 0 0 20px;
  font-size: 14px;
  color: #bfbfbf;
}

.course-cards-container {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
}

#home-courses-heading {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 40px;
}

#home-courses-heading-left {
  display: flex;
  flex: 1;
  flex-direction: row;
  gap: 20px;
  align-items: center;
  justify-content: flex-start;
}

#home-courses-heading-right {
  display: flex;
  flex-direction: row;
}

.section-link {
  padding: 10px;
  font-size: 14px;
  color: unset;
  text-decoration: none;
  cursor: pointer;
  border-radius: 9px;
}

.section-link:hover {
  background-color: $csm-link-hover;
}
