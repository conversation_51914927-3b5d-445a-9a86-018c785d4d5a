@use "base/variables" as *;

#word-of-the-day-container {
  padding-top: 10px;
  margin-top: 25px;
  border-top: 2px solid #e7e7e7;
}

#word-of-the-day-card {
  padding: 30px 30px 15px;
  margin-bottom: 30px;
  border: 2px solid #e7e7e7;
  border-radius: 20px;
  box-shadow: 0 4px 4px rgb(198 198 198 / 25%);
}

.word-of-the-day-action-container {
  display: flex;
  flex-direction: row;
  gap: 8px;
  align-items: center;
  justify-content: space-between;
}

.word-of-the-day-input-container {
  display: flex;
  flex-direction: row;
  gap: 8px;
  align-items: center;
  justify-content: space-between;
}

.word-of-the-day-status.unselected {
  color: $csm-danger;
}

.word-of-the-day-status.selected {
  color: $csm-green-darkened;
}

.random-word-picker-btn {
  color: $csm-green-darkened;
}

.word-of-the-day-select {
  width: 150px;
  max-width: 200px;
  padding: 6px;
  margin-right: 5px;
  font-family: <PERSON>ser<PERSON>, sans-serif;
  text-align-last: center;
  border: none;
  border-radius: 6px;
}

.word-of-the-day-select-container {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.word-of-the-day-submit-container {
  display: flex;
  flex-direction: row;
  gap: 15px;
  align-items: center;
}

.word-of-the-day-status-bar {
  display: flex;
  justify-content: space-between;
  padding: 10px 0;
  font-size: 0.9rem;
}

.word-of-the-day-status-text {
  color: $csm-danger;
}

.word-of-the-day-icon {
  width: 2em;
  height: 2em;
}

.word-of-the-day-deadline:not(.passed) {
  color: $csm-green-darkened;
}

.word-of-the-day-deadline.passed {
  color: $csm-danger-darkened;
}
