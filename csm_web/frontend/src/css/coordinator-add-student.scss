/* Styles for the coordinator add student modal. */

@use "base/variables" as *;

$modal-width: 65vw;
$modal-height: 75vh;
$modal-effective-height: calc($modal-height - $modal-padding-y);

.coordinator-add-student-modal {
  width: $modal-width;
  height: $modal-height;
  overflow: hidden;
}

.coordinator-add-student-modal-contents {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  height: $modal-effective-height;
  padding-bottom: 20px;
}

@media screen and (width <= 800px) {
  .coordinator-add-student-modal {
    width: 85vw;
  }
}

@media screen and (width <= 1000px) {
  .coordinator-add-student-modal {
    width: 75vw;
  }
}

.coordinator-email-content {
  width: 95%;
  height: 75%;
  max-height: 75%;
  margin: auto;
  overflow: auto;
}

.coordinator-email-input-list {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.coordinator-email-input-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin: 5px 0;
}

.coordinator-email-input-item .inline-plus-sign,
.coordinator-email-response-item .inline-plus-sign {
  line-height: 1.1rem;
  color: #777;
  cursor: pointer;
  user-select: none;
}

.coordinator-email-response-item .inline-plus-sign {
  margin-right: 3px;
}

.coordinator-email-input-item .inline-plus-sign:hover,
.coordinator-email-response-item .inline-plus-sign:hover {
  color: #aaa;
}

.coordinator-email-input {
  width: 250px;
  max-width: 100%;
  margin-left: 5px;
}

.coordinator-email-input-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
  justify-content: center;
}

.coordinator-email-secondary-input-buttons {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: center;
  justify-content: center;
}

.coordinator-email-input-buttons-top {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.coordinator-email-input-add,
.coordinator-email-modal-button {
  display: flex;
  flex-direction: row;
  justify-content: space-evenly;
  padding: 8px 20px;
  margin: 3px auto;
  font-size: 16px;
  font-weight: 600;
  color: white;
  background-color: $csm-neutral;
  border: none;
  border-radius: 8px;
}

.coordinator-email-modal-button {
  padding: 8px;
  margin: auto;
  font-size: 0.95rem;
  font-weight: normal;
  background-color: $csm-neutral;
}

.coordinator-email-input-add:hover,
.coordinator-email-modal-button:hover {
  background-color: $csm-neutral-darkened;
}

.coordinator-email-input-submit {
  display: flex;
  flex-direction: row;
  justify-content: space-evenly;
  padding: 10px 20px;
  margin: 10px auto;
  font-size: 16px;
  font-weight: 600;
  color: white;
  background-color: $csm-green;
  border: none;
  border-radius: 8px;
}

.coordinator-email-input-submit:hover {
  background-color: $csm-green-darkened;
}

#coordinator-add-student-form input:not(.lock-validation):invalid {
  background-color: #fdd;
}

.coordinator-email-response-list {
  display: flex;
  flex-direction: column;
  width: fit-content;
  margin: auto;
  text-align: left;
}

.coordinator-email-response-item {
  display: contents;
}

.coordinator-email-response-email {
  margin-left: 3px;
}

.coordinator-email-response-email-status {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: left;
}

.coordinator-email-response-form-info {
  margin-bottom: 2px;
  margin-left: 20px;
  font-size: 0.9rem;
  color: red;
}

.coordinator-email-response-form {
  display: flex;
  flex-direction: column;
  margin-top: 5px;
  margin-left: 40px;
}

.coordinator-email-response-form label {
  margin: 5px;
  font-size: 0.9rem;
}

.coordinator-email-response-status {
  margin-left: 5px;
}

.coordinator-email-response-status-ok,
.coordinator-email-response-status-conflict,
.coordinator-email-response-status-banned,
.coordinator-email-response-capacity {
  display: flex;
  align-items: center;
  justify-content: left;
}

.coordinator-email-response-status-ok {
  color: green;
}

.coordinator-email-response-status-ok-icon {
  width: 1.25rem;
  height: 1.25rem;
  padding-right: 6px;
  margin: 0 !important;
}

.coordinator-email-response-status-conflict-icon {
  width: 1.5rem;
  height: 1.5rem;
  padding-right: 6px;
  margin: 0 !important;
  fill: red;
}

.coordinator-email-response-status-conflict,
.coordinator-email-response-status-banned,
.coordinator-email-response-capacity {
  color: red;
}

.coordinator-email-response-container {
  display: grid;
  grid-template-columns: 3fr 2fr;
  grid-row-gap: 12px;
  margin: 10px 0;
}

.coordinator-email-response-capacity-container {
  margin: 10px 0;
}

.coordinator-email-response-hr {
  margin-top: -2px;
  margin-bottom: 10px;
  border: 1px solid #ccc;
}

.coordinator-email-response-head {
  display: contents;
}

.coordinator-email-response-head-left {
  grid-column: 1;
}

.coordinator-email-response-head-right {
  display: flex;
  flex-direction: row;
  grid-column: 2;
  column-gap: 5px;
  align-items: center;
  justify-content: space-around;
  font-size: 0.9rem;
  text-align: center;
}

.coordinator-email-response-head-right-item {
  flex: 1;
}

.coordinator-email-response-item-container {
  display: contents;
}

.coordinator-email-response-item-left {
  grid-column: 1;
  margin-left: 15px;
}

.coordinator-email-response-item-left-email {
  padding-bottom: 6px;
}

.coordinator-email-response-item-left-detail {
  margin-left: 25px;
  font-size: 0.9rem;
  color: #555;
}

.coordinator-email-response-item-right {
  display: flex;
  flex-direction: row;
  grid-column: 2;
  row-gap: 3px;
  align-items: center;
  justify-content: space-around;
}

.coordinator-email-response-item-right > * {
  flex: 1;
}
