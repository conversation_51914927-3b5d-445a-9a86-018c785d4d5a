@use "base/variables" as *;

$modal-width: 80vw;
$modal-height: 75vh;

.resource-wrapper-container {
  max-width: 1064px;
  margin: 20px auto;
}

.resource-table {
  min-width: 750px;
}

.resource-table-container {
  overflow-x: auto;
}

.resource-container {
  height: 100%;
  padding: 24px 0;
  margin: 16px auto;
  background-color: $csm-resources;
  border-radius: 8px;
  box-shadow:
    0 4px 6px -1px rgb(0 0 0 / 10%),
    0 2px 0 -1px rgb(0 0 0 / 6%);
}

.resource-container,
.resource-wrapper-header {
  display: grid;
  grid-template-columns: repeat(2, minmax(50px, 1fr)) 2fr 2fr 1fr 40px;
  grid-column: 1 / -1;
  column-gap: 10px;
  width: 100%;
  text-align: center;
}

.resource-edit-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: $modal-height;
}

.resource-edit-content-wrapper {
  // expand container of worksheets/links as much as possible
  flex: 1;
  overflow: auto;
}

.resource-edit-content {
  min-width: 600px;
}

.resource-edit-modal {
  width: $modal-width !important;
  height: $modal-height !important;
}

.resource-edit-modal .modal-contents {
  overflow: hidden;
}

.resource-wrapper-header {
  margin-bottom: 4px;
}

.resource-info,
.resource-wrapper-header > div {
  display: flex;
  flex-flow: row wrap;
  align-items: center;
  justify-content: center;
  padding: 0 8px;
  margin: auto;
}

.resource-row {
  hyphens: auto;
}

.resource-row-render {
  display: grid;
  grid-template-columns: repeat(3, minmax(100px, 1fr));
  grid-column: 1 / -1;
}

.resource-worksheet-files-container {
  display: grid;
  grid-auto-columns: 2fr 1.5fr;
  row-gap: 10px;
  align-content: center;
  align-items: center;
  justify-content: center;
}

.resource-solution a,
.resource-link a {
  text-decoration: none;
}

.resource-worksheet-file,
.resource-solution,
.resource-worksheet-file a:link,
.resource-solution a:link,
.resource-worksheet-file a:visited,
.resource-solution a:visited,
.resource-link,
.resource-link a:link,
.resource-link a:visited {
  color: $csm-green-darkened;
}

.resource-worksheet-file {
  grid-column: 1;
}

.resource-worksheet-file a:link {
  text-decoration: underline;
}

.resource-solution {
  grid-column: 2;
  width: 80px;
  height: fit-content;
  padding: 2px;
  margin-left: 10px;
  font-size: 14px;
  font-weight: 600;
  text-align: center;
  border: 2px solid $csm-green-darkened;
  border-radius: 5px;
}

.resource-worksheet-file a:hover,
.resource-solution a:hover,
.resource-link a:hover {
  color: #4e7a5d;
  cursor: pointer;
  border-color: #4e7a5d;
}

.topic {
  padding: 4px 8px;
  margin: 4px;
  background-color: $csm-green;
  border-radius: 4px;
}

#resource-edit-inner {
  display: flex;
  flex-direction: row;
  justify-content: center;
  width: 100%;
}

.resource-info-edit {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 4px;
  margin: 4px;
}

.resource-info-edit input {
  width: 144px;
}

.resource-edit-head-item {
  align-items: center;
}

.resource-worksheet-edit-item > input {
  width: 100%;
  min-width: 100px;
  max-width: 180px;
}

.topics-tooltip-wrapper {
  position: relative;
  font-weight: initial;
}

.resource-worksheet-container {
  display: grid;
  gap: 10px;
  width: 70%;
  margin: 20px auto;
}

@media screen and (width <= 1200px) {
  .resource-worksheet-container {
    width: 90%;
  }
}

.resource-worksheet-head {
  display: grid;

  /* name, worksheet, solution, delete */
  grid-template-columns: repeat(3, 1fr) 20px;
  column-gap: 10px;
  text-align: center;
}

.resource-link-head {
  display: grid;

  /* name, url, delete */
  grid-template-columns: 1fr 1fr 20px;
  column-gap: 10px;
  text-align: center;
}

.resource-worksheet {
  display: grid;
  grid-template-columns: repeat(3, 1fr) 20px;
  column-gap: 10px;
}

.resource-link,
.resource-link-edit {
  display: grid;
  column-gap: 10px;
}

.resource-link-edit {
  grid-template-columns: 1fr 1fr 20px;
}

.resource-links-container {
  display: grid;
  row-gap: 10px;
  align-content: center;
}

.resource-worksheet-edit-file {
  display: flex;
  margin: auto 0;
}

.resource-worksheet-edit-item {
  margin: auto;
}

.file-upload input[type="file"] {
  display: none;
}

.file-upload {
  display: flex;
  flex: 100%;
  flex-direction: row;
  padding: 8px 12px;
  font-size: 90%;
  cursor: pointer;
  background-color: white;
  border: 2px solid #ccc;
  border-right-width: 1px;

  /* take up as much space as possible */
}

.file-upload .file-upload-label {
  flex: 100%;
  width: 100px;
  overflow-x: hidden;
  text-align: left;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-upload:hover {
  color: $csm-green-darkened;
  border-color: $csm-neutral-darkened;
}

.file-upload:only-child {
  border-right-width: 2px;
}

button.clear-file {
  display: inline-block;
  padding: 0 10px;
  background-color: #f8f8f8;
  border: none;
  border: 2px solid #ccc;
  border-left: none;
}

button.clear-file:hover {
  color: red;
  background-color: #eee;
}

.delete-worksheet {
  padding: 0;
  font-size: 18px;
  color: $csm-neutral-darkened;
  background-color: transparent;
  border: none;
}

.delete-worksheet:hover {
  color: red;
}

.upload-icon {
  width: 1em;
  margin-right: 5px !important;
}

.resource-wrapper-header > div {
  font-size: 16px;
  font-weight: 600;
}

.resource-worksheet-actions-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.resource-buttons-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-right: 16px;
}

.resource-button {
  font-size: 18px;
  color: $csm-neutral;
  border: none;
}

#edit-icon:hover {
  color: $csm-green-darkened;
}

#delete-icon:hover {
  color: $csm-danger-darkened;
}

.resource-tab-error-text {
  display: flex;
  flex-direction: row;
  gap: 4px;
  align-items: center;
  color: red;
  text-align: center;
}

#save-icon {
  margin: 0 !important;
}

#resource-button-submit:hover {
  background-color: $csm-green-darkened;
}

.resource-validation-error {
  display: flex;
  flex-direction: row;
  gap: 4px;
  align-items: center;
  margin: 3px 2px 4px;
  font-size: 80%;
  color: red;
}

.resource-table-options {
  display: flex;
  flex-flow: row wrap;
  gap: 8px;
  align-items: center;
  justify-content: center;
  margin-bottom: 24px;
}

#toggle-view-edit-input {
  color: $csm-green;
  cursor: pointer;
}

.resource-delete-confirmation .resource-delete-text {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
  text-align: center;
}

.resource-edit-footer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
