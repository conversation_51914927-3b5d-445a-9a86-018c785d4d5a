/* Section enrollment */

@use "base/variables" as *;
@use "base/button";

.section-card {
  display: flex;
  flex-direction: column;
  width: 320px;
  margin: 0 auto 50px;
  overflow-x: hidden;
  border-radius: 20px;
  box-shadow: 0 5px 50px rgb(0 0 0 / 15%);
}

.section-card.full {
  background-color: #eaeaea;
}

.section-card-contents {
  position: relative;
  flex: 1;
  padding: 10px 20px 0;
}

.section-card-contents p {
  color: #727272;
}

.section-card-contents svg,
.section-card-icon-placeholder {
  margin-right: 5px;
  vertical-align: bottom;
}

.section-card-additional-times {
  display: block;
}

.section-card-icon-placeholder {
  display: inline-block;
}

.section-card-footer {
  @extend %button;

  padding: 5px !important;
  font-size: 1.3em;
  text-align: center;
  text-transform: uppercase;
  border-radius: 0 !important;
}

/* Specify div because it's an <a> element for coordinators which links to section management page */
.section-card.full div.section-card-footer {
  color: transparent;
  cursor: inherit;
  user-select: none;
  background-color: inherit;
}

.section-card-description {
  position: absolute;
  top: 10px;
  right: 10px;
  min-width: 50px;
  padding: 2px 5px;
  font-size: 12px;
  color: white;
  text-align: center;
  background-color: $csm-student;
  border-radius: 8px;
}

.enroll-confirm-modal-contents {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
  justify-content: center;
}

#day-selector {
  display: grid;
  grid-template-columns: repeat(auto-fit, $day-btn-size);
  grid-auto-rows: max-content;
  grid-gap: 10px;
  justify-content: center;
}

.data-export-modal {
  display: flex;
  flex-direction: column;
  height: 90%;
}

.data-export-modal-header {
  display: flex;
  flex: 0.2 0.2 0;
  align-items: center;
  justify-content: center;
}

.data-export-modal-selection {
  display: flex;
  flex: 1 1 55%;
  align-items: center;
  justify-content: center;
}

.data-export-modal-download {
  display: flex;
  flex: 1 1 30%;
  align-items: center;
  justify-content: center;
}

.data-export-checkbox-grid {
  display: grid;
  grid-template-rows: repeat(auto-fit, 30px);
  grid-template-columns: repeat(3, 1fr);
  gap: 1%;
  place-items: center center;
  width: 100%;
  height: 50%;
}

.data-export-checkbox {
  align-items: center;
  justify-content: center;
  width: 40%;
}

#course-section-controls {
  top: 0;
  flex-grow: 0.5;
  align-self: flex-start;
  min-width: 50%;
  margin-top: 40px;
}

#course-section-controls > * {
  margin: 0 0 10px;
}

#course-coord-buttons {
  display: flex;
  flex-direction: row;
  gap: 8px;
  align-items: center;
  justify-content: center;
  width: max-content;
  margin: 0 auto;
}

#course-enrollment-open-status {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin: 20px 0;
  color: #6d6d6d;
}

.course-title {
  font-size: 2.5em;
  font-weight: 400;
  color: #6d6d6d;
  text-align: center;
}

#show-unavailable-toggle {
  display: block;
  color: #727272;
  text-align: center;
}

#show-unavailable-toggle > input {
  margin-right: 5px;
  vertical-align: top;
}

.day-btn {
  width: $day-btn-size;
  height: $day-btn-size;
  font-size: 1.3em;
  color: #717171;
  text-align: center;
  cursor: pointer;
  background-color: white;
  border: 2px solid $csm-green;
  border-radius: calc($day-btn-size / 2);
  outline: none;
}

.day-btn:hover {
  background-color: #fafafa;
}

.day-btn.active {
  color: white;
  background-color: $csm-green;
}

#course-section-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  grid-gap: 10px;
  width: 75vw;
  margin: 40px auto;
}

#course-section-list-empty {
  color: #646464;
  text-align: center;
}

$create-section-modal-height: 65vh;

.create-section-modal {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: $create-section-modal-height;
}

#create-section-form {
  // form should take up all of the space
  flex: 1;
  overflow: auto;
}

#create-section-form #non-spacetime-fields,
#create-section-form .spacetime-fields {
  display: grid;
  grid-template-areas:
    ". ."
    "last last";
  grid-template-columns: max-content max-content;
  grid-gap: 20px;
  align-items: center;
  padding-left: 0;
}

#create-section-form-contents > .spacetime-fields-header {
  min-width: 100%;
  margin: 0 0 10px;
  color: #646464;
  border-bottom: 2px solid #646464;
}

#create-section-form-contents {
  max-width: max-content;
  margin: auto;
}

#create-section-form-contents > * {
  margin-bottom: 20px;
}

#create-section-form label[name="description"],
#create-section-form label[name="time"] {
  grid-area: last;
}

.create-section-submit-container {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: center;
  justify-content: center;
  margin-top: 20px;
}

#create-section-form .create-section-submit-container {
  grid-area: submit;
}

.create-section-validation-text-container {
  margin-bottom: 8px;
  color: red;
}

.create-section-validation-text {
  margin-left: 4px;
}
