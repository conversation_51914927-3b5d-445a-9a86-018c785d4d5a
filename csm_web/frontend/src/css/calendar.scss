@use "base/variables" as *;

$calendar-interval-height: 26px;

/*
Hierarchy:

.calendar-container
  > .calendar-header
    > .calendar-header-left
    > .calendar-day-header
  > .calendar-body
    > .calendar-day-container
      > .calendar-labels
        > .calendar-label
      > .calendar-day
        > .calendar-day-interval
*/

.calendar-container {
  box-sizing: border-box;
}

.calendar-header {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: stretch;
  border-bottom: 1px solid $calendar-header-border;
}

.calendar-day-header {
  box-sizing: border-box;
  flex: 1;
  width: 100%;
  padding: 5px 0;
  font-weight: bold;
  color: $calendar-fg;
  text-align: center;
  background-color: $calendar-header-bg;
  border-right: 1px solid $calendar-border;
  border-left: 1px solid $calendar-border;
}

.calendar-body {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-around;
  overflow: hidden;
  touch-action: none;
}

.calendar-day-container {
  flex: 1;
  width: 100%;
}

.calendar-day {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: stretch;
  padding: 8px 0;
  font-size: 14px;
  color: $calendar-fg;
  text-align: center;
  background-color: $calendar-day-bg;
  border: 1px solid $calendar-border;
}

.calendar-day-interval {
  box-sizing: border-box;
  width: 100%;
  height: $calendar-interval-height;
  font-size: 14px;
  font-weight: bold;
  color: $calendar-fg;
  text-align: center;
  border-top: 1px solid $calendar-border;
  border-bottom: 1px solid $calendar-border;
}

.calendar-header-left {
  box-sizing: border-box;
  width: 50px;
  padding-right: 5px;
  font-size: 85%;
  text-align: right;
}

.calendar-labels {
  box-sizing: border-box;
  width: 50px;
  padding-right: 5px;
}

.calendar-label {
  position: relative;
  top: -5px;
  height: $calendar-interval-height;
  font-size: 75%;
  color: $calendar-label-fg;
  text-align: right;
}

/*
Hierarchy:

.calendar-events
  > .calendar-event-container
    > .calendar-event (.calendar-event-hover / .calendar-event-select)
      > .calendar-event-detail
*/

.calendar-events {
  width: 100%;
}

.calendar-event-container,
.calendar-transparent-event-container {
  position: relative;
  width: 100%;
  height: 0;
}

.calendar-event,
.calendar-transparent-event,
.calendar-creating-event {
  position: relative;
  box-sizing: border-box;
  padding: 0 3px 3px 0;
}

.calendar-transparent-event-container {
  pointer-events: none;
}

.calendar-event-detail {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  padding-top: 3px;
  overflow: hidden;
  user-select: none;
  background-color: lightblue;
  transition:
    background-color ease 100ms,
    box-shadow ease 100ms;
}

.calendar-event-detail.flush {
  padding-top: 0;
}

.calendar-transparent-event .calendar-event-detail {
  background-color: $calendar-dragging-event;
}

.calendar-creating-event .calendar-event-detail {
  background-color: $calendar-creating-event;
}

.calendar-event-hover .calendar-event-detail {
  background-color: $calendar-hover-event;
  box-shadow: 2px 2px 2px $calendar-hover-shadow;
}

.calendar-event-select .calendar-event-detail {
  background-color: $calendar-select-event;
}

.calendar-event-linked .calendar-event-detail {
  filter: brightness(1.1);
}
