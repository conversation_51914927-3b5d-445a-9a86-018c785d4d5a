FROM node:18

# install inotify-tools for webpack reloading
# RUN apt install inotify-tools

RUN mkdir -p /opt/csm_web \
    # change ownership of /opt/csm_web to install dependencies
    && chown node:node /opt/csm_web

WORKDIR /opt/csm_web

USER node

# install npm dependencies
COPY --chown=node:node package.json package-lock.json ./
RUN npm install && npm cache clean --force
ENV PATH /opt/csm_web/node_modules/.bin:$PATH

WORKDIR /opt/csm_web/app

# change to root to allow permission changes in entrypoint
USER root

# specify entrypoint to execute pre-command tasks
COPY ./docker-node-entrypoint.sh ./
ENTRYPOINT ["./docker-node-entrypoint.sh"]

# start continuous compilation
CMD ["npm", "run", "watch"]
