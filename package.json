{"name": "csm_web", "version": "1.0.0", "description": "", "main": "index.tsx", "scripts": {"dev": "webpack --mode development", "build:js": "webpack --mode production", "build": "npm run build:js", "watch": "webpack --mode development --watch", "test": "jest", "cypress:open": "cypress open --env DOCKER_PREFIX='docker compose exec django'", "cypress:run": "cypress run --e2e --record=false --env DOCKER_PREFIX='docker compose exec django' --config video=false,screenshotOnRunFailure=false"}, "heroku-run-build-script": true, "repository": {"type": "git", "url": "git+https://github.com/csmberkeley/csm_web.git"}, "keywords": [], "author": "", "license": "MIT", "bugs": {"url": "https://github.com/csmberkeley/csm_web/issues"}, "homepage": "https://github.com/csmberkeley/csm_web#readme", "devDependencies": {"@babel/core": "^7.20.2", "@babel/eslint-parser": "^7.19.1", "@babel/plugin-transform-runtime": "^7.19.6", "@babel/preset-env": "^7.20.2", "@babel/preset-react": "^7.18.6", "@babel/preset-typescript": "^7.18.6", "@svgr/webpack": "^6.5.1", "@tanstack/react-query-devtools": "^4.2.3", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@types/jest": "^29.5.3", "@types/js-cookie": "^3.0.2", "@types/lodash": "^4.14.175", "@types/luxon": "^3.3.0", "@types/random-words": "^1.1.2", "@types/react-router-dom": "^5.3.3", "@typescript-eslint/eslint-plugin": "^5.43.0", "@typescript-eslint/parser": "^5.43.0", "babel-loader": "^8.2.2", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-lodash": "^3.3.4", "babel-plugin-transform-class-properties": "^6.24.1", "babel-polyfill": "^6.26.0", "css-loader": "^6.8.1", "css-minimizer-webpack-plugin": "^5.0.1", "csso-cli": "^3.0.0", "csv-parse": "^5.5.3", "cypress": "^13.14.1", "cypress-pipe": "^2.0.0", "eslint": "^8.50.0", "eslint-config-prettier": "^8.10.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-cypress": "^2.12.1", "eslint-plugin-import": "^2.28.1", "eslint-plugin-react": "^7.31.10", "eslint-plugin-react-hooks": "^4.6.0", "jest": "^29.6.2", "jest-environment-jsdom": "^29.0.3", "lodash-webpack-plugin": "^0.11.6", "mini-css-extract-plugin": "^2.7.6", "prettier": "^3.0.3", "random-words": "^1.1.2", "sass": "^1.64.2", "sass-loader": "^13.3.2", "style-loader": "^3.3.3", "stylelint": "^15.10.3", "stylelint-config-css-modules": "^4.3.0", "stylelint-config-recess-order": "^4.3.0", "stylelint-config-standard-scss": "^11.0.0", "stylelint-order": "^6.0.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^4.8.4", "webpack": "^5.76.0", "webpack-cli": "^4.10.0"}, "dependencies": {"@babel/runtime": "^7.20.1", "@tanstack/react-query": "^4.2.3", "@types/node": "^14.17.5", "@types/react": "^18.0.14", "@types/react-dom": "^18.0.5", "js-cookie": "^2.2.1", "lodash": "^4.17.21", "luxon": "^3.3.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.3.0"}}