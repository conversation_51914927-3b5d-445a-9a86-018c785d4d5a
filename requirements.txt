asgiref==3.8.1 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
asttokens==3.0.0 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
boto3==1.37.1 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
botocore==1.37.1 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
certifi==2025.1.31 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
cffi==1.17.1 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0" and platform_python_implementation != "PyPy"
charset-normalizer==3.4.1 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
colorama==0.4.6 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0" and sys_platform == "win32"
cryptography==44.0.1 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
decorator==5.2.1 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
defusedxml==0.7.1 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
dj-database-url==2.3.0 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
django-csp==3.8 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
django-extensions==3.2.3 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
django-heroku==0.3.1 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
django-postgres-extra==2.0.9rc11 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
django-storages==1.14.5 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
django==5.1.6 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
djangorestframework-camel-case==1.4.2 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
djangorestframework==3.15.2 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
drf-nested-forms==1.1.8 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
executing==2.2.0 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
factory-boy==3.3.3 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
faker==36.1.1 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
freezegun==1.5.1 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
gunicorn==22.0.0 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
idna==3.10 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
iniconfig==2.0.0 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
ipython==8.32.0 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
jedi==0.19.2 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
jmespath==1.0.1 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
matplotlib-inline==0.1.7 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
networkx==3.4.2 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
oauthlib==3.2.2 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
packaging==24.2 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
parso==0.8.4 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
pexpect==4.9.0 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0" and (sys_platform != "win32" and sys_platform != "emscripten")
pluggy==1.5.0 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
prompt-toolkit==3.0.50 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
psycopg2-binary==2.9.10 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
psycopg2==2.9.10 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
ptyprocess==0.7.0 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0" and (sys_platform != "win32" and sys_platform != "emscripten")
pure-eval==0.2.3 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
pycparser==2.22 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0" and platform_python_implementation != "PyPy"
pygments==2.19.1 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
pyjwt==2.10.1 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
pytest-django==4.10.0 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
pytest==8.3.4 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
python-dateutil==2.9.0.post0 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
python3-openid==3.2.0 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
requests-oauthlib==2.0.0 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
requests==2.32.3 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
s3transfer==0.11.2 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
sentry-sdk==2.22.0 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
six==1.17.0 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
social-auth-app-django==5.4.3 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
social-auth-core==4.5.6 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
sqlparse==0.5.3 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
stack-data==0.6.3 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
traitlets==5.14.3 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
typing-extensions==4.12.2 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
tzdata==2025.1 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
urllib3==2.3.0 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
wcwidth==0.2.13 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
whitenoise==6.9.0 ; python_full_version >= "3.12.4" and python_full_version < "3.13.0"
