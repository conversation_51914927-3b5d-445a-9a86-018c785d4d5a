name: Django CI

on:
  push:
    branches: [master]
  pull_request:
    branches: [master]
  workflow_dispatch:

env:
  PYTHON_VERSION: 3.12

jobs:
  pytest:
    name: "pytest"
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: csm_web_dev
        ports: ["5432:5432"]
        options: --health-cmd pg_isready --health-interval 10s --health-timeout 5s --health-retries 5

    steps:
      - uses: actions/checkout@v2
      - name: Parse Python version from runtime.txt
        run: |
          sed 's/[^0-9.]//g' runtime.txt | head > .python-version
      - name: Set up Python
        uses: actions/setup-python@v4
      - name: Install Poetry
        uses: snok/install-poetry@v1
        with:
          virtualenvs-create: true
          virtualenvs-in-project: true
          installer-parallel: true
      - name: Install dependencies with Poetry
        run: |
          poetry install --no-root --with=prod
          # add virtual environment to path for future steps
          echo .venv/bin >> $GITHUB_PATH
      - name: Run Tests
        env:
          SECRET_KEY: ${{ secrets.SECRET_KEY }}
          DJANGO_ENV: dev
          POSTGRES_PASSWORD: postgres
        run: |
          pytest csm_web
