name: Cypress E2E

on:
  push:
    branches: [master]
  pull_request:
    branches: [master]
  workflow_dispatch:

env:
  PYTHON_VERSION: 3.12

jobs:
  cypress-run-chrome:
    name: Chrome tests (${{ matrix.containers }})
    runs-on: ubuntu-latest
    strategy:
      matrix:
        containers: [1, 2, 3, 4]
    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: csm_web_dev
        ports: ["5432:5432"]
        options: --health-cmd pg_isready --health-interval 10s --health-timeout 5s --health-retries 5
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Set up Python ${{ env.PYTHON_VERSION }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
      - name: Install Python dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
      - name: Install npm dependencies
        run: |
          npm ci
      - name: Run Cypress
        uses: cypress-io/github-action@v6
        with:
          record: true
          build: npm run build
          start: python csm_web/manage.py runserver
          wait-on: http://localhost:8000
          browser: chrome
          parallel: true
          group: Tests on Chrome
        env:
          SECRET_KEY: ${{ secrets.SECRET_KEY }}
          DJANGO_ENV: dev
          POSTGRES_PASSWORD: postgres
          CYPRESS_RECORD_KEY: ${{ secrets.CYPRESS_RECORD_KEY }}
          # pass GitHub token to allow accurately detecting a build vs a re-run build
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          # better cypress run titles
          COMMIT_INFO_MESSAGE: ${{github.event.pull_request.title}}
          COMMIT_INFO_SHA: ${{github.event.pull_request.head.sha}}

  cypress-run-firefox:
    name: Firefox tests (${{ matrix.containers }})
    runs-on: ubuntu-latest
    strategy:
      matrix:
        containers: [1, 2, 3, 4]
    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: csm_web_dev
        ports: ["5432:5432"]
        options: --health-cmd pg_isready --health-interval 10s --health-timeout 5s --health-retries 5
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Set up Python ${{ env.PYTHON_VERSION }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
      - name: Install Python dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
      - name: Install npm dependencies
        run: |
          npm ci
      - name: Run Cypress
        uses: cypress-io/github-action@v6
        with:
          record: true
          build: npm run build
          start: python csm_web/manage.py runserver
          wait-on: http://localhost:8000
          browser: firefox
          parallel: true
          group: Tests on Firefox
        env:
          SECRET_KEY: ${{ secrets.SECRET_KEY }}
          DJANGO_ENV: dev
          POSTGRES_PASSWORD: postgres
          CYPRESS_RECORD_KEY: ${{ secrets.CYPRESS_RECORD_KEY }}
          # pass GitHub token to allow accurately detecting a build vs a re-run build
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          # better cypress run titles
          COMMIT_INFO_MESSAGE: ${{github.event.pull_request.title}}
          COMMIT_INFO_SHA: ${{github.event.pull_request.head.sha}}
