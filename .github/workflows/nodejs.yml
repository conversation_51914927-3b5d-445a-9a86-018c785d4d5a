# This workflow will do a clean install of node dependencies, build the source code and run tests across different versions of node
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-nodejs-with-github-actions

name: Node.js CI

on:
  push:
    branches: [master]
  pull_request:
    branches: [master]
  workflow_dispatch:

env:
  NODE_VERSION: 16.x

jobs:
  build:
    name: npm build
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Use Node.js ${{ env.NODE_VERSION }}
        uses: actions/setup-node@v1
        with:
          node-version: ${{ env.NODE_VERSION }}
      - run: npm ci
      - run: npm run build --if-present
      - name: Run linters
        uses: wearerequired/lint-action@v1
        with:
          github_token: ${{ secrets.github_token }}
          eslint: true
          eslint_dir: csm_web/frontend/src
          eslint_extensions: js,jsx,ts,tsx
          prettier: true
          prettier_dir: csm_web/frontend/src
  test:
    name: npm test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Use Node.js ${{ env.NODE_VERSION }}
        uses: actions/setup-node@v1
        with:
          node-version: ${{ env.NODE_VERSION }}
      - run: npm ci
      - run: npm run build --if-present
      - run: npm run test
